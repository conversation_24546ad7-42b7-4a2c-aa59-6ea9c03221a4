package config

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/api"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/go-playground/validator/v10"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	userRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/api/rest"
	userUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/app"
	userPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/repo/pg"
	userRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/repo/repo"

	restMdlwr "github.com/JosueDiazC/fhyona-v2-backend/internal/services/middleware/rest"

	authRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/api/rest"
	authUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/app"
	authPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/repo/pg"
	authRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/repo/repo"

	brandRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/api/rest"
	brandUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/app"
	brandPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/repo/pg"
	brandRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/repo/repo"

	categoryRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/api/rest"
	categoryUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/app"
	categoryPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/repo/pg"
	categoryRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/repo/repo"

	channelRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/api/rest"
	channelUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/app"
	channelPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/repo/pg"
	channelRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/repo/repo"

	workAreaRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/api/rest"
	workAreaUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/app"
	workAreaPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/repo/pg"
	workAreaRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/repo/repo"

	operationRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/api/rest"
	operationUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/app"
	operationPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/repo/pg"
	operationRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/repo/repo"

	productionFlowRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/api/rest"
	productionFlowUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/app"
	productionFlowPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/repo/pg"
	productionFlowRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/repo/repo"

	measurementUnitRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/api/rest"
	measurementUnitUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/app"
	measurementUnitPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/repo/pg"
	measurementUnitRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/repo/repo"

	productRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/api/rest"
	productUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/app"
	productPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/repo/pg"
	productRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/repo/repo"

	recipeRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/api/rest"
	recipeUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/app"
	recipePg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/repo/pg"
	recipeRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/repo/repo"

	warehouseRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/api/rest"
	warehouseUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/app"
	warehousePg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/repo/pg"
	warehouseRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/repo/repo"

	inventoryRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/api/rest"
	inventoryUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/app"
	inventoryPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/repo/pg"
	inventoryRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/repo/repo"

	businessLineRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/api/rest"
	businessLineUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/app"
	businessLinePg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/repo/pg"
	businessLineRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/repo/repo"

	areaRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/api/rest"
	areaUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/app"
	areaPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/repo/pg"
	areaRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/repo/repo"

	sellerRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/api/rest"
	sellerUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/app"
	sellerPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/repo/pg"
	sellerRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/repo/repo"

	clientRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/api/rest"
	clientUsecase "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/app"
	clientPg "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/repo/pg"
	clientRepo "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/repo/repo"
)

type BootstrapConfig struct {
	HTTP     *http.ServeMux
	Log      *logrus.Logger
	Validate *validator.Validate
	Config   *viper.Viper
	PgDB     *pgxpool.Pool
}

func Bootstrap(config *BootstrapConfig) {
	utils.Init()

	userPg := userPg.NewUserPostgreRepo(config.PgDB)
	userRepo := userRepo.NewUserRepository(userPg)
	userUsecase := userUsecase.NewUserUsecase(userRepo)
	userHandler := userRest.NewUserHandler(config.Log, config.Validate, userUsecase)

	restMdlwr := restMdlwr.NewHTTPMiddleware(config.Log, config.Config, userUsecase)

	authPg := authPg.NewAuthPostgreRepo(config.PgDB)
	authRepo := authRepo.NewAuthRepository(authPg)
	authUsecase := authUsecase.NewAuthUsecase(authRepo)
	authHandler := authRest.NewAuthHandler(config.Log, config.Validate, authUsecase)

	brandPg := brandPg.NewBrandPostgreRepo(config.PgDB)
	brandRepo := brandRepo.NewBrandRepository(brandPg)
	brandUsecase := brandUsecase.NewBrandUsecase(brandRepo)
	brandHandler := brandRest.NewBrandHandler(config.Log, config.Validate, brandUsecase)

	categoryPg := categoryPg.NewCategoryPostgreRepo(config.PgDB)
	categoryRepo := categoryRepo.NewCategoryRepository(categoryPg)
	categoryUsecase := categoryUsecase.NewCategoryUsecase(categoryRepo)
	categoryHandler := categoryRest.NewCategoryHandler(config.Log, config.Validate, categoryUsecase)

	channelPg := channelPg.NewChannelPostgreRepo(config.PgDB)
	channelRepo := channelRepo.NewChannelRepository(channelPg)
	channelUsecase := channelUsecase.NewChannelUsecase(channelRepo)
	channelHandler := channelRest.NewChannelHandler(config.Log, config.Validate, channelUsecase)

	workAreaPg := workAreaPg.NewWorkAreaPostgreRepo(config.PgDB)
	workAreaRepo := workAreaRepo.NewWorkAreaRepository(workAreaPg)
	workAreaUsecase := workAreaUsecase.NewWorkAreaUsecase(workAreaRepo)
	workAreaHandler := workAreaRest.NewWorkAreaHandler(config.Log, config.Validate, workAreaUsecase)

	operationPg := operationPg.NewOperationPostgreRepo(config.PgDB)
	operationRepo := operationRepo.NewOperationRepository(operationPg)
	operationUsecase := operationUsecase.NewOperationUsecase(operationRepo)
	operationHandler := operationRest.NewOperationHandler(config.Log, config.Validate, operationUsecase)

	productionFlowPg := productionFlowPg.NewProductionFlowPostgreRepo(config.PgDB)
	productionFlowRepo := productionFlowRepo.NewProductionFlowRepository(productionFlowPg)
	productionFlowUsecase := productionFlowUsecase.NewProductionFlowUsecase(productionFlowRepo)
	productionFlowHandler := productionFlowRest.NewProductionFlowHandler(config.Log, config.Validate, productionFlowUsecase)

	measurementUnitPgRepo := measurementUnitPg.NewMeasurementUnitPostgreRepo(config.PgDB)
	measurementUnitRepository := measurementUnitRepo.NewMeasurementUnitRepository(measurementUnitPgRepo)
	measurementUnitUsecaseInstance := measurementUnitUsecase.NewMeasurementUnitUsecase(measurementUnitRepository)
	measurementUnitHandler := measurementUnitRest.NewMeasurementUnitHandler(config.Log, config.Validate, measurementUnitUsecaseInstance)

	unitMeasurementCategoryPgRepo := measurementUnitPg.NewUnitMeasurementCategoryPostgreRepo(config.PgDB)
	unitMeasurementCategoryRepository := measurementUnitRepo.NewUnitMeasurementCategoryRepository(unitMeasurementCategoryPgRepo)
	unitMeasurementCategoryUsecaseInstance := measurementUnitUsecase.NewUnitMeasurementCategoryUsecase(unitMeasurementCategoryRepository)
	unitMeasurementCategoryHandler := measurementUnitRest.NewUnitMeasurementCategoryHandler(config.Log, config.Validate, unitMeasurementCategoryUsecaseInstance)

	productPg := productPg.NewProductPostgreRepo(config.PgDB)
	productRepo := productRepo.NewProductRepository(productPg)
	productUsecase := productUsecase.NewProductUsecase(productRepo)
	productHandler := productRest.NewProductHandler(config.Log, config.Validate, productUsecase)

	recipePg := recipePg.NewRecipePostgreRepo(config.PgDB)
	recipeRepo := recipeRepo.NewRecipeRepository(recipePg)
	recipeUsecase := recipeUsecase.NewRecipeUsecase(recipeRepo)
	recipeHandler := recipeRest.NewRecipeHandler(config.Log, config.Validate, recipeUsecase)

	warehousePg := warehousePg.NewWarehousePostgreRepo(config.PgDB)
	warehouseRepo := warehouseRepo.NewWarehouseRepository(warehousePg)
	warehouseUsecase := warehouseUsecase.NewWarehouseUsecase(warehouseRepo)
	warehouseHandler := warehouseRest.NewWarehouseHandler(config.Log, config.Validate, warehouseUsecase)

	inventoryPg := inventoryPg.NewInventoryPostgreRepo(config.PgDB)
	inventoryRepo := inventoryRepo.NewInventoryRepository(inventoryPg)
	inventoryUsecase := inventoryUsecase.NewInventoryUsecase(inventoryRepo)
	inventoryHandler := inventoryRest.NewInventoryHandler(config.Log, config.Validate, inventoryUsecase)

	businessLinePg := businessLinePg.NewBusinessLinePostgreRepo(config.PgDB)
	businessLineRepo := businessLineRepo.NewBusinessLineRepository(businessLinePg)
	businessLineUsecase := businessLineUsecase.NewBusinessLineUsecase(businessLineRepo)
	businessLineHandler := businessLineRest.NewBusinessLineHandler(config.Log, config.Validate, businessLineUsecase)

	areaPg := areaPg.NewAreaPostgreRepo(config.PgDB)
	areaRepo := areaRepo.NewAreaRepository(areaPg)
	areaUsecase := areaUsecase.NewAreaUsecase(areaRepo)
	areaHandler := areaRest.NewAreaHandler(config.Log, config.Validate, areaUsecase)

	sellerPg := sellerPg.NewSellerPostgreRepo(config.PgDB)
	sellerRepo := sellerRepo.NewSellerRepository(sellerPg)
	sellerUsecase := sellerUsecase.NewSellerUsecase(sellerRepo)
	sellerHandler := sellerRest.NewSellerHandler(config.Log, config.Validate, sellerUsecase)

	clientPg := clientPg.NewClientPostgreRepo(config.PgDB)
	clientRepo := clientRepo.NewClientRepository(clientPg)
	clientUsecase := clientUsecase.NewClientUsecase(clientRepo, sellerRepo)
	clientHandler := clientRest.NewClientHandler(config.Log, config.Validate, clientUsecase)

	routeConfig := api.NewRouteConfig(
		config.HTTP,
		config.Log,
		restMdlwr,
		userHandler,
		authHandler,
		brandHandler,
		categoryHandler,
		channelHandler,
		workAreaHandler,
		operationHandler,
		productionFlowHandler,
		measurementUnitHandler,
		unitMeasurementCategoryHandler,
		productHandler,
		recipeHandler,
		warehouseHandler,
		inventoryHandler,
		businessLineHandler,
		areaHandler,
		sellerHandler,
		clientHandler,
	)

	routeConfig.SetupRoutes()
}

package api

import (
	"net/http"

	"github.com/sirupsen/logrus"

	areaRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/api/rest"
	authRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/api/rest"
	brandRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/api/rest"
	businessLineRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/api/rest"
	categoryRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/api/rest"
	channelRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/api/rest"
	clientRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/api/rest"
	inventoryRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/api/rest"
	measurementUnitRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/api/rest"
	operationRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/api/rest"
	productRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/api/rest"
	productionFlowRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/api/rest"
	productionRequestRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/api/rest"
	recipeRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/api/rest"
	sellerRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/api/rest"
	userRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/api/rest"
	warehouseRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/api/rest"
	workAreaRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/api/rest"

	restMdlwr "github.com/JosueDiazC/fhyona-v2-backend/internal/services/middleware/rest"
	restUtils "github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type RouteConfig interface {
	SetupRoutes()
}

type routeConfig struct {
	http                           *http.ServeMux
	log                            *logrus.Logger
	middlware                      restMdlwr.HTTPMiddleware
	userHandler                    userRest.UserHandler
	authHandler                    authRest.AuthHandler
	brandHandler                   brandRest.BrandHandler
	categoryHandler                categoryRest.CategoryHandler
	channelHandler                 channelRest.ChannelHandler
	workAreaHandler                workAreaRest.WorkAreaHandler
	operationHandler               operationRest.OperationHandler
	productionFlowHandler          productionFlowRest.ProductionFlowHandler
	measurementUnitHandler         measurementUnitRest.MeasurementUnitHandler
	unitMeasurementCategoryHandler measurementUnitRest.UnitMeasurementCategoryHandler
	productHandler                 productRest.ProductHandler
	recipeHandler                  recipeRest.RecipeHandler
	warehouseHandler               warehouseRest.WarehouseHandler
	inventoryHandler               inventoryRest.InventoryHandler
	businessLineHandler            businessLineRest.BusinessLineHandler
	areaHandler                    areaRest.AreaHandler
	sellerHandler                  sellerRest.SellerHandler
	clientHandler                  clientRest.ClientHandler
	productionRequestHandler       productionRequestRest.ProductionRequestHandler
}

// SetupRoutes implements RouteConfig.
func (r *routeConfig) SetupRoutes() {
	routes := http.NewServeMux()
	baseMiddleware := restUtils.ComposeHMiddleware(
		r.middlware.Cors,
		r.middlware.CorrelationID,
		r.middlware.Logging,
		r.middlware.Client,
	)

	authMiddleware := restUtils.ComposeHFMiddleware(
		r.middlware.Authenticated,
	)

	r.http.Handle("/", baseMiddleware(routes))

	routes.HandleFunc("POST /api/v1/users", authMiddleware(r.userHandler.Create))
	routes.HandleFunc("PUT /api/v1/users", authMiddleware(r.userHandler.Update))
	routes.HandleFunc("GET /api/v1/users/{id}", authMiddleware(r.userHandler.GetById))
	routes.HandleFunc("GET /api/v1/users", authMiddleware(r.userHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/users/{id}", authMiddleware(r.userHandler.Delete))

	routes.HandleFunc("POST /api/v1/brands", authMiddleware(r.brandHandler.Create))
	routes.HandleFunc("PUT /api/v1/brands", authMiddleware(r.brandHandler.Update))
	routes.HandleFunc("GET /api/v1/brands/{id}", authMiddleware(r.brandHandler.GetById))
	routes.HandleFunc("GET /api/v1/brands", authMiddleware(r.brandHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/brands/{id}", authMiddleware(r.brandHandler.Delete))
	routes.HandleFunc("GET /api/v1/brands/validate-code/{code}", authMiddleware(r.brandHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/measurement-units", authMiddleware(r.measurementUnitHandler.Create))
	routes.HandleFunc("PUT /api/v1/measurement-units", authMiddleware(r.measurementUnitHandler.Update))
	routes.HandleFunc("GET /api/v1/measurement-units/{id}", authMiddleware(r.measurementUnitHandler.GetById))
	routes.HandleFunc("GET /api/v1/measurement-units", authMiddleware(r.measurementUnitHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/measurement-units/{id}", authMiddleware(r.measurementUnitHandler.Delete))
	routes.HandleFunc("GET /api/v1/measurement-units/validate-code/{code}", authMiddleware(r.measurementUnitHandler.ValidateCode))
	routes.HandleFunc("GET /api/v1/measurement-units/validate-abbreviation/{abbreviation}", authMiddleware(r.measurementUnitHandler.ValidateAbbreviation))

	routes.HandleFunc("POST /api/v1/unit-measurement-categories", authMiddleware(r.unitMeasurementCategoryHandler.Create))
	routes.HandleFunc("PUT /api/v1/unit-measurement-categories", authMiddleware(r.unitMeasurementCategoryHandler.Update))
	routes.HandleFunc("GET /api/v1/unit-measurement-categories/{id}", authMiddleware(r.unitMeasurementCategoryHandler.GetById))
	routes.HandleFunc("GET /api/v1/unit-measurement-categories", authMiddleware(r.unitMeasurementCategoryHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/unit-measurement-categories/{id}", authMiddleware(r.unitMeasurementCategoryHandler.Delete))
	routes.HandleFunc("GET /api/v1/unit-measurement-categories/validate-code/{code}", authMiddleware(r.unitMeasurementCategoryHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/categories", authMiddleware(r.categoryHandler.Create))
	routes.HandleFunc("PUT /api/v1/categories", authMiddleware(r.categoryHandler.Update))
	routes.HandleFunc("GET /api/v1/categories/{id}", authMiddleware(r.categoryHandler.GetById))
	routes.HandleFunc("GET /api/v1/categories", authMiddleware(r.categoryHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/categories/{id}", authMiddleware(r.categoryHandler.Delete))
	routes.HandleFunc("GET /api/v1/categories/subcategories/{id}", authMiddleware(r.categoryHandler.GetSubcategories))
	routes.HandleFunc("GET /api/v1/categories/code/{code}/subcategories", authMiddleware(r.categoryHandler.GetSubcategoriesByCode))
	routes.HandleFunc("GET /api/v1/categories/parents", authMiddleware(r.categoryHandler.GetParentCategories))
	routes.HandleFunc("GET /api/v1/categories/details/{id}", authMiddleware(r.categoryHandler.GetCategoryWithDetails))
	routes.HandleFunc("GET /api/v1/categories/code/{code}/details", authMiddleware(r.categoryHandler.GetCategoryWithDetailsByCode))
	routes.HandleFunc("GET /api/v1/categories/validate-code/{code}", authMiddleware(r.categoryHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/work-areas", authMiddleware(r.workAreaHandler.Create))
	routes.HandleFunc("PUT /api/v1/work-areas", authMiddleware(r.workAreaHandler.Update))
	routes.HandleFunc("GET /api/v1/work-areas/{id}", authMiddleware(r.workAreaHandler.GetById))
	routes.HandleFunc("GET /api/v1/work-areas", authMiddleware(r.workAreaHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/work-areas/{id}", authMiddleware(r.workAreaHandler.Delete))
	routes.HandleFunc("GET /api/v1/work-areas/validate-code/{code}", authMiddleware(r.workAreaHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/operations", authMiddleware(r.operationHandler.Create))
	routes.HandleFunc("PUT /api/v1/operations", authMiddleware(r.operationHandler.Update))
	routes.HandleFunc("GET /api/v1/operations/{id}", authMiddleware(r.operationHandler.GetById))
	routes.HandleFunc("GET /api/v1/operations", authMiddleware(r.operationHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/operations/{id}", authMiddleware(r.operationHandler.Delete))
	routes.HandleFunc("GET /api/v1/operations/validate-code/{code}", authMiddleware(r.operationHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/production-flows", authMiddleware(r.productionFlowHandler.Create))
	routes.HandleFunc("POST /api/v1/production-flows/with-activities", authMiddleware(r.productionFlowHandler.CreateWithActivities))
	routes.HandleFunc("PUT /api/v1/production-flows", authMiddleware(r.productionFlowHandler.Update))
	routes.HandleFunc("GET /api/v1/production-flows/{id}", authMiddleware(r.productionFlowHandler.GetById))
	routes.HandleFunc("GET /api/v1/production-flows", authMiddleware(r.productionFlowHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/production-flows/{id}", authMiddleware(r.productionFlowHandler.Delete))
	routes.HandleFunc("GET /api/v1/production-flows/activities/{id}", authMiddleware(r.productionFlowHandler.GetWithActivities))
	routes.HandleFunc("GET /api/v1/production-flows/validate-code/{code}", authMiddleware(r.productionFlowHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/products", authMiddleware(r.productHandler.Create))
	routes.HandleFunc("PUT /api/v1/products", authMiddleware(r.productHandler.Update))
	routes.HandleFunc("GET /api/v1/products/{id}", authMiddleware(r.productHandler.GetById))
	routes.HandleFunc("GET /api/v1/products", authMiddleware(r.productHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/products/{id}", authMiddleware(r.productHandler.Delete))
	routes.HandleFunc("GET /api/v1/products/validate-code/{code}", authMiddleware(r.productHandler.ValidateCode))
	routes.HandleFunc("GET /api/v1/products/validate-commercial-name/{commercialName}", authMiddleware(r.productHandler.ValidateCommercialName))
	routes.HandleFunc("GET /api/v1/products/validate-sku-code/{skuCode}", authMiddleware(r.productHandler.ValidateSKUCode))
	routes.HandleFunc("GET /api/v1/products/category/{categoryCode}", authMiddleware(r.productHandler.GetProductsByCategoryCode))

	routes.HandleFunc("POST /api/v1/recipes", authMiddleware(r.recipeHandler.Create))
	routes.HandleFunc("PUT /api/v1/recipes", authMiddleware(r.recipeHandler.Update))
	routes.HandleFunc("GET /api/v1/recipes/{id}", authMiddleware(r.recipeHandler.GetById))
	routes.HandleFunc("GET /api/v1/recipes", authMiddleware(r.recipeHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/recipes/{id}", authMiddleware(r.recipeHandler.Delete))
	routes.HandleFunc("GET /api/v1/recipes/validate-code/{code}", authMiddleware(r.recipeHandler.ValidateCode))
	routes.HandleFunc("GET /api/v1/recipes/validate-name/{name}", authMiddleware(r.recipeHandler.ValidateName))

	routes.HandleFunc("POST /api/v1/warehouses", authMiddleware(r.warehouseHandler.Create))
	routes.HandleFunc("PUT /api/v1/warehouses", authMiddleware(r.warehouseHandler.Update))
	routes.HandleFunc("GET /api/v1/warehouses/{id}", authMiddleware(r.warehouseHandler.GetById))
	routes.HandleFunc("GET /api/v1/warehouses", authMiddleware(r.warehouseHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/warehouses/{id}", authMiddleware(r.warehouseHandler.Delete))
	routes.HandleFunc("GET /api/v1/warehouses/validate-code/{code}", authMiddleware(r.warehouseHandler.ValidateCode))
	routes.HandleFunc("GET /api/v1/warehouses/validate-name/{name}", authMiddleware(r.warehouseHandler.ValidateName))

	routes.HandleFunc("POST /api/v1/inventory", authMiddleware(r.inventoryHandler.Create))
	routes.HandleFunc("PUT /api/v1/inventory", authMiddleware(r.inventoryHandler.Update))
	routes.HandleFunc("GET /api/v1/inventory/{id}", authMiddleware(r.inventoryHandler.GetById))
	routes.HandleFunc("GET /api/v1/inventory", authMiddleware(r.inventoryHandler.GetAll))
	routes.HandleFunc("GET /api/v1/inventory/warehouse/{warehouse_id}", authMiddleware(r.inventoryHandler.GetByWarehouseID))
	routes.HandleFunc("GET /api/v1/inventory/product/{product_id}", authMiddleware(r.inventoryHandler.GetByProductID))
	routes.HandleFunc("GET /api/v1/inventory/batch/{batch_code}", authMiddleware(r.inventoryHandler.GetByBatchCode))
	routes.HandleFunc("GET /api/v1/inventory/low-stock", authMiddleware(r.inventoryHandler.GetLowStockItems))
	routes.HandleFunc("GET /api/v1/inventory/expiring", authMiddleware(r.inventoryHandler.GetExpiringItems))
	routes.HandleFunc("DELETE /api/v1/inventory/{id}", authMiddleware(r.inventoryHandler.Delete))
	routes.HandleFunc("GET /api/v1/inventory/validate-batch/{warehouse_id}/{product_id}/{batch_code}", authMiddleware(r.inventoryHandler.ValidateBatchCode))

	routes.HandleFunc("POST /api/v1/business-lines", authMiddleware(r.businessLineHandler.Create))
	routes.HandleFunc("PUT /api/v1/business-lines", authMiddleware(r.businessLineHandler.Update))
	routes.HandleFunc("GET /api/v1/business-lines", authMiddleware(r.businessLineHandler.GetAll))
	routes.HandleFunc("GET /api/v1/business-lines/validate/code/{code}", authMiddleware(r.businessLineHandler.ValidateCode))
	routes.HandleFunc("GET /api/v1/business-lines/query/parent-lines", authMiddleware(r.businessLineHandler.GetParentLines))
	routes.HandleFunc("GET /api/v1/business-lines/query/by-code/{code}/sublines", authMiddleware(r.businessLineHandler.GetSublinesByCode))
	routes.HandleFunc("GET /api/v1/business-lines/query/by-code/{code}/details", authMiddleware(r.businessLineHandler.GetBusinessLineWithDetailsByCode))
	routes.HandleFunc("GET /api/v1/business-lines/{id}", authMiddleware(r.businessLineHandler.GetById))
	routes.HandleFunc("DELETE /api/v1/business-lines/{id}", authMiddleware(r.businessLineHandler.Delete))
	routes.HandleFunc("GET /api/v1/business-lines/{id}/sublines", authMiddleware(r.businessLineHandler.GetSublines))
	routes.HandleFunc("GET /api/v1/business-lines/{id}/details", authMiddleware(r.businessLineHandler.GetBusinessLineWithDetails))

	routes.HandleFunc("POST /api/v1/areas", authMiddleware(r.areaHandler.Create))
	routes.HandleFunc("PUT /api/v1/areas", authMiddleware(r.areaHandler.Update))
	routes.HandleFunc("GET /api/v1/areas", authMiddleware(r.areaHandler.GetAll))
	routes.HandleFunc("GET /api/v1/areas/validate/code/{code}", authMiddleware(r.areaHandler.ValidateCode))
	routes.HandleFunc("GET /api/v1/areas/{id}", authMiddleware(r.areaHandler.GetById))
	routes.HandleFunc("DELETE /api/v1/areas/{id}", authMiddleware(r.areaHandler.Delete))

	routes.HandleFunc("POST /api/v1/sellers", authMiddleware(r.sellerHandler.Create))
	routes.HandleFunc("PUT /api/v1/sellers", authMiddleware(r.sellerHandler.Update))
	routes.HandleFunc("GET /api/v1/sellers", authMiddleware(r.sellerHandler.GetAll))
	routes.HandleFunc("GET /api/v1/sellers/validate/identity-document/{identityDocument}", authMiddleware(r.sellerHandler.ValidateIdentityDocument))
	routes.HandleFunc("GET /api/v1/sellers/{id}", authMiddleware(r.sellerHandler.GetById))
	routes.HandleFunc("DELETE /api/v1/sellers/{id}", authMiddleware(r.sellerHandler.Delete))

	routes.HandleFunc("POST /api/v1/clients", authMiddleware(r.clientHandler.Create))
	routes.HandleFunc("PUT /api/v1/clients", authMiddleware(r.clientHandler.Update))
	routes.HandleFunc("GET /api/v1/clients", authMiddleware(r.clientHandler.GetAll))
	routes.HandleFunc("GET /api/v1/clients/validate/document/{document}", authMiddleware(r.clientHandler.ValidateDocument))
	routes.HandleFunc("GET /api/v1/clients/{id}", authMiddleware(r.clientHandler.GetById))
	routes.HandleFunc("DELETE /api/v1/clients/{id}", authMiddleware(r.clientHandler.Delete))

	routes.HandleFunc("POST /api/v1/production-requests", authMiddleware(r.productionRequestHandler.Create))
	routes.HandleFunc("PUT /api/v1/production-requests", authMiddleware(r.productionRequestHandler.Update))
	routes.HandleFunc("GET /api/v1/production-requests", authMiddleware(r.productionRequestHandler.GetAll))
	routes.HandleFunc("GET /api/v1/production-requests/validate/code/{code}", authMiddleware(r.productionRequestHandler.ValidateCode))
	routes.HandleFunc("GET /api/v1/production-requests/{id}", authMiddleware(r.productionRequestHandler.GetById))
	routes.HandleFunc("DELETE /api/v1/production-requests/{id}", authMiddleware(r.productionRequestHandler.Delete))

	routes.HandleFunc("POST /api/v1/channels", authMiddleware(r.channelHandler.Create))
	routes.HandleFunc("PUT /api/v1/channels", authMiddleware(r.channelHandler.Update))
	routes.HandleFunc("GET /api/v1/channels", authMiddleware(r.channelHandler.GetAll))
	routes.HandleFunc("GET /api/v1/channels/validate/name/{name}", authMiddleware(r.channelHandler.ValidateName))
	routes.HandleFunc("GET /api/v1/channels/{id}", authMiddleware(r.channelHandler.GetById))
	routes.HandleFunc("DELETE /api/v1/channels/{id}", authMiddleware(r.channelHandler.Delete))

	routes.HandleFunc("POST /api/v1/auth/login", r.authHandler.Login)
	routes.HandleFunc("POST /api/v1/auth/logout", r.authHandler.Logout)
	routes.HandleFunc("GET /api/v1/auth/is_logged_in", authMiddleware(r.authHandler.IsLoggedIn))
}

func NewRouteConfig(
	http *http.ServeMux,
	log *logrus.Logger,
	middleware restMdlwr.HTTPMiddleware,
	userHandler userRest.UserHandler,
	authHandler authRest.AuthHandler,
	brandHandler brandRest.BrandHandler,
	categoryHandler categoryRest.CategoryHandler,
	channelHandler channelRest.ChannelHandler,
	workAreaHandler workAreaRest.WorkAreaHandler,
	operationHandler operationRest.OperationHandler,
	productionFlowHandler productionFlowRest.ProductionFlowHandler,
	measurementUnitHandler measurementUnitRest.MeasurementUnitHandler,
	unitMeasurementCategoryHandler measurementUnitRest.UnitMeasurementCategoryHandler,
	productHandler productRest.ProductHandler,
	recipeHandler recipeRest.RecipeHandler,
	warehouseHandler warehouseRest.WarehouseHandler,
	inventoryHandler inventoryRest.InventoryHandler,
	businessLineHandler businessLineRest.BusinessLineHandler,
	areaHandler areaRest.AreaHandler,
	sellerHandler sellerRest.SellerHandler,
	clientHandler clientRest.ClientHandler,
	productionRequestHandler productionRequestRest.ProductionRequestHandler,
) RouteConfig {
	return &routeConfig{
		http:                           http,
		log:                            log,
		middlware:                      middleware,
		userHandler:                    userHandler,
		authHandler:                    authHandler,
		brandHandler:                   brandHandler,
		categoryHandler:                categoryHandler,
		channelHandler:                 channelHandler,
		workAreaHandler:                workAreaHandler,
		operationHandler:               operationHandler,
		productionFlowHandler:          productionFlowHandler,
		measurementUnitHandler:         measurementUnitHandler,
		unitMeasurementCategoryHandler: unitMeasurementCategoryHandler,
		productHandler:                 productHandler,
		recipeHandler:                  recipeHandler,
		warehouseHandler:               warehouseHandler,
		inventoryHandler:               inventoryHandler,
		businessLineHandler:            businessLineHandler,
		areaHandler:                    areaHandler,
		sellerHandler:                  sellerHandler,
		clientHandler:                  clientHandler,
		productionRequestHandler:       productionRequestHandler,
	}
}

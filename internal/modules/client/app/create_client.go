package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.ClientUsecase.
func (c *clientUsecase) Create(ctx context.Context, clientCreate model.ClientCreate) (string, error) {
	// Check if name already exists
	nameExists, err := c.repo.CountByProp(ctx, "name", clientCreate.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if client name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.ClientConflictNamef("Client name already exists", nil, nil)
	}

	// Check if document already exists
	documentExists, err := c.repo.CountByProp(ctx, "document", clientCreate.Document)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if document exists", err, nil)
	}

	if documentExists > 0 {
		return "", model.ClientConflictDocumentf("Document already exists", nil, nil)
	}

	// Validate seller exists if seller_id is provided
	if clientCreate.SellerID != nil && *clientCreate.SellerID != "" {
		_, err := c.sellerRepo.GetByProp(ctx, "id", *clientCreate.SellerID)
		if err != nil {
			return "", utils.BadRequestf("Seller not found", err, nil)
		}
	}

	// Set default state if not provided
	state := clientCreate.State
	if state == nil {
		defaultState := "active"
		state = &defaultState
	}

	newClient := model.Client{
		ID:                 utils.UniqueId(),
		Name:               clientCreate.Name,
		FatherName:         clientCreate.FatherName,
		MotherName:         clientCreate.MotherName,
		ClientType:         clientCreate.ClientType,
		DocumentType:       clientCreate.DocumentType,
		Document:           clientCreate.Document,
		Ubication:          clientCreate.Ubication,
		SocialReason:       clientCreate.SocialReason,
		CommercialName:     clientCreate.CommercialName,
		Condition:          clientCreate.Condition,
		State:              state,
		HasRetentionRegime: clientCreate.HasRetentionRegime,
		BusinessLineID:     clientCreate.BusinessLineID,
		SubBusinessLineID:  clientCreate.SubBusinessLineID,
		ChannelID:          clientCreate.ChannelID,
		SellerID:           clientCreate.SellerID,
		ContactName:        clientCreate.ContactName,
		Email:              clientCreate.Email,
		Phone:              clientCreate.Phone,
	}

	err = c.repo.Create(ctx, newClient)
	if err != nil {
		return "", err
	}

	return newClient.ID, nil
}

package model

import "context"

type ClientUsecase interface {
	Create(ctx context.Context, client ClientCreate) (string, error)
	Update(ctx context.Context, client ClientUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Client, error)
	GetAll(ctx context.Context) ([]Client, error)
	Delete(ctx context.Context, id string) error
	ValidateDocument(ctx context.Context, document string) error
}

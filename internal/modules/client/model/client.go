package model

import (
	"time"
)

// Seller represents seller information embedded in client responses
type Seller struct {
	ID                     string `json:"id"`
	Name                   string `json:"name"`
	FatherName             string `json:"father_name"`
	MotherName             string `json:"mother_name"`
	IdentityDocumentNumber string `json:"identity_document_number"`
	State                  string `json:"state"`
}

type Client struct {
	ID                 string
	Name               string
	FatherName         *string
	MotherName         *string
	ClientType         string // "natural" or "juridica"
	DocumentType       string
	Document           string
	Ubication          *string
	SocialReason       *string
	CommercialName     *string
	Condition          *string
	State              *string
	HasRetentionRegime *bool
	BusinessLineID     *string
	SubBusinessLineID  *string
	ChannelID          *string
	SellerID           *string
	Seller             *Seller
	ContactName        *string
	Email              *string
	Phone              *string
	CreatedAt          *time.Time
	UpdatedAt          *time.Time
	DeletedAt          *time.Time
}

type ClientCreate struct {
	Name               string
	FatherName         *string
	MotherName         *string
	ClientType         string // "natural" or "juridica"
	DocumentType       string
	Document           string
	Ubication          *string
	SocialReason       *string
	CommercialName     *string
	Condition          *string
	State              *string
	HasRetentionRegime *bool
	BusinessLineID     *string
	SubBusinessLineID  *string
	ChannelID          *string
	SellerID           *string
	ContactName        *string
	Email              *string
	Phone              *string
}

type ClientUpdate struct {
	ID                 string
	Name               string
	FatherName         *string
	MotherName         *string
	ClientType         string // "natural" or "juridica"
	DocumentType       string
	Document           string
	Ubication          *string
	SocialReason       *string
	CommercialName     *string
	Condition          *string
	State              *string
	HasRetentionRegime *bool
	BusinessLineID     *string
	SubBusinessLineID  *string
	ChannelID          *string
	SellerID           *string
	ContactName        *string
	Email              *string
	Phone              *string
}

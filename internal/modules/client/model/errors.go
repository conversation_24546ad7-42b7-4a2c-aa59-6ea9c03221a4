package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	ClientConflictCode         utils.ErrCode = utils.ClientCode + iota
	ClientConflictNameCode
	ClientConflictDocumentCode
	ClientNotFoundCode
)

func ClientConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ClientConflictCode, message, err, details)
}

func ClientConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ClientConflictNameCode, message, err, details)
}

func ClientConflictDocumentf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ClientConflictDocumentCode, message, err, details)
}

func ClientNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ClientNotFoundCode, message, err, details)
}

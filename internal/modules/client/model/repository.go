package model

import "context"

type ClientRepository interface {
	Create(ctx context.Context, client Client) error
	Update(ctx context.Context, client Client) error
	GetByProp(ctx context.Context, prop string, value string) (*Client, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Client, error)
	Delete(ctx context.Context, id string) error
}

package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type ClientHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateDocument(w http.ResponseWriter, r *http.Request)
}

type clientHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.ClientUsecase
}

func NewClientHandler(log *logrus.Logger, validator *validator.Validate, useCase model.ClientUsecase) ClientHandler {
	return &clientHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}

package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements ClientHandler.
func (c *clientHandler) Delete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")

	err := c.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to delete client")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// ValidateDocument implements ClientHandler.
func (c *clientHandler) ValidateDocument(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	document := r.PathValue("document")

	err := c.useCase.ValidateDocument(ctx, document)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Document validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

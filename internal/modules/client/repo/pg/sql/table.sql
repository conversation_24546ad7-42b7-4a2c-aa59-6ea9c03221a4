CREATE TABLE dev.clients (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    father_name <PERSON><PERSON><PERSON><PERSON>(255),
    mother_name VA<PERSON><PERSON><PERSON>(255),
    client_type VARCHAR(50) NOT NULL CHECK (client_type IN ('natural', 'juridica')),
    document_type VARCHAR(100) NOT NULL,
    document VARCHAR(255) NOT NULL UNIQUE,
    ubication TEXT,
    social_reason VARCHAR(255),
    commercial_name VARCHAR(255),
    condition VARCHAR(100),
    state VARCHAR(50) DEFAULT 'active',
    has_retention_regime BOOLEAN,
    business_line_id VARCHAR(255),
    sub_business_line_id VARCHAR(255),
    channel_id VARCHAR(255),
    contact_name VA<PERSON>HA<PERSON>(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (business_line_id) REFERENCES dev.business_lines(id),
    FOREIG<PERSON> KEY (sub_business_line_id) REFERENCES dev.business_lines(id),
    FOREIGN KEY (channel_id) REFERENCES dev.channels(id)
);

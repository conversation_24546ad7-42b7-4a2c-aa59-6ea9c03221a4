-- Production Requests table
CREATE TABLE dev.production_requests (
    id VARCHAR(255) PRIMARY KEY,
    code VA<PERSON>HAR(255) NOT NULL UNIQUE,
    client_id VARCHAR(255) NOT NULL,
    expected_date TIMESTAMPTZ,
    priority VARCHAR(50) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    state VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (state IN ('pending', 'approved', 'rejected', 'in_progress', 'completed')),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (client_id) REFERENCES dev.clients(id)
);

-- Production Request Items junction table
CREATE TABLE dev.production_request_items (
    id VARCHAR(255) PRIMARY KEY,
    production_request_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL CHECK (quantity > 0),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (production_request_id) REFERENCES dev.production_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES dev.products(id),
    UNIQUE(production_request_id, product_id)
);

-- Indexes for better performance
CREATE INDEX idx_production_requests_client_id ON dev.production_requests(client_id);
CREATE INDEX idx_production_requests_code ON dev.production_requests(code);
CREATE INDEX idx_production_requests_expected_date ON dev.production_requests(expected_date);
CREATE INDEX idx_production_requests_priority ON dev.production_requests(priority);
CREATE INDEX idx_production_requests_state ON dev.production_requests(state);
CREATE INDEX idx_production_request_items_production_request_id ON dev.production_request_items(production_request_id);
CREATE INDEX idx_production_request_items_product_id ON dev.production_request_items(product_id);

-- Comments for documentation
COMMENT ON TABLE dev.production_requests IS 'Production requests from clients with expected delivery dates and priorities';
COMMENT ON TABLE dev.production_request_items IS 'Items requested in each production request with quantities';
COMMENT ON COLUMN dev.production_requests.code IS 'Unique identifier code for the production request';
COMMENT ON COLUMN dev.production_requests.client_id IS 'Foreign key reference to clients table';
COMMENT ON COLUMN dev.production_requests.expected_date IS 'Expected delivery date for the production request';
COMMENT ON COLUMN dev.production_requests.priority IS 'Priority level: low, medium, high, urgent';
COMMENT ON COLUMN dev.production_requests.state IS 'Current state: pending, approved, rejected, in_progress, completed';
COMMENT ON COLUMN dev.production_request_items.quantity IS 'Quantity of the product requested';

-- Migration script to update production request states
-- Description: Update state constraint to include new states: approved and rejected
-- Date: 2025-08-07

-- Drop the existing check constraint
ALTER TABLE dev.production_requests 
DROP CONSTRAINT IF EXISTS production_requests_state_check;

-- Add the new check constraint with updated states
ALTER TABLE dev.production_requests 
ADD CONSTRAINT production_requests_state_check 
CHECK (state IN ('pending', 'approved', 'rejected', 'in_progress', 'completed'));

-- Update the comment to reflect the new states
COMMENT ON COLUMN dev.production_requests.state IS 'Current state: pending, approved, rejected, in_progress, completed';

package model

import "time"

type ProductionRequest struct {
	ID           string
	Code         string
	ClientID     string
	ExpectedDate *time.Time
	Priority     string // "low", "medium", "high", "urgent"
	State        string // "pending", "in_progress", "completed", "cancelled"
	Requests     []ProductionRequestItem
	CreatedAt    *time.Time
	UpdatedAt    *time.Time
	DeletedAt    *time.Time
}

type ProductionRequestItem struct {
	ID                  string
	ProductionRequestID string
	ProductID           string
	Quantity            float64
}

type ProductionRequestCreate struct {
	Code         string
	ClientID     string
	ExpectedDate *time.Time
	Priority     string
	State        string
	Requests     []ProductionRequestItemCreate
}

type ProductionRequestItemCreate struct {
	ProductID string
	Quantity  float64
}

type ProductionRequestUpdate struct {
	ID           string
	Code         string
	ClientID     string
	ExpectedDate *time.Time
	Priority     string
	State        string
	Requests     []ProductionRequestItemUpdate
}

type ProductionRequestItemUpdate struct {
	ID        string
	ProductID string
	Quantity  float64
}

type ProductionRequestWithDetails struct {
	ProductionRequest ProductionRequest
	Client            interface{} // Will be populated with client details
	RequestItems      []ProductionRequestItemWithProduct
}

type ProductionRequestItemWithProduct struct {
	ProductionRequestItem ProductionRequestItem
	Product               interface{} // Will be populated with product details
}

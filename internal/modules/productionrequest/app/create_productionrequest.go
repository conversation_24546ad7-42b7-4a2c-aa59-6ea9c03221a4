package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) Create(ctx context.Context, productionRequest model.ProductionRequestCreate) (string, error) {
	// Validate priority
	if !isValidPriority(productionRequest.Priority) {
		return "", model.ProductionRequestInvalidPriorityf("Invalid priority. Must be one of: low, medium, high, urgent", nil, nil)
	}

	// Validate state
	if !isValidState(productionRequest.State) {
		return "", model.ProductionRequestInvalidStatef("Invalid state. Must be one of: pending, approved, rejected, in_progress, completed", nil, nil)
	}

	// Validate expected date (should be in the future)
	if productionRequest.ExpectedDate != nil && productionRequest.ExpectedDate.Before(time.Now()) {
		return "", model.ProductionRequestInvalidDatef("Expected date must be in the future", nil, nil)
	}

	// Check if code already exists
	codeExists, err := p.repo.CountByProp(ctx, "code", productionRequest.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if production request code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.ProductionRequestConflictCodef("Production request code already exists", nil, nil)
	}

	// Validate client exists
	_, err = p.clientRepo.GetByProp(ctx, "id", productionRequest.ClientID)
	if err != nil {
		return "", model.ProductionRequestInvalidClientf("Client does not exist", err, nil)
	}

	// Validate products exist and quantities are positive
	for _, item := range productionRequest.Requests {
		if item.Quantity <= 0 {
			return "", model.ProductionRequestInvalidQuantityf("Product quantity must be greater than 0", nil, nil)
		}

		_, err := p.productRepo.GetByProp(ctx, "id", item.ProductID)
		if err != nil {
			return "", model.ProductionRequestInvalidProductf("Product does not exist", err, nil)
		}
	}

	// Set default values
	priority := productionRequest.Priority
	if priority == "" {
		priority = "medium"
	}

	state := productionRequest.State
	if state == "" {
		state = "pending"
	}

	// Generate ID and create production request
	id := utils.UniqueId()
	productionRequestEntity := model.ProductionRequest{
		ID:           id,
		Code:         productionRequest.Code,
		ClientID:     productionRequest.ClientID,
		ExpectedDate: productionRequest.ExpectedDate,
		Priority:     priority,
		State:        state,
		Requests:     convertItemsCreateToItems(productionRequest.Requests),
	}

	err = p.repo.Create(ctx, productionRequestEntity)
	if err != nil {
		return "", err
	}

	return id, nil
}

func isValidPriority(priority string) bool {
	validPriorities := []string{"low", "medium", "high", "urgent"}
	for _, valid := range validPriorities {
		if priority == valid {
			return true
		}
	}
	return false
}

func isValidState(state string) bool {
	validStates := []string{"pending", "approved", "rejected", "in_progress", "completed"}
	for _, valid := range validStates {
		if state == valid {
			return true
		}
	}
	return false
}

func convertItemsCreateToItems(createItems []model.ProductionRequestItemCreate) []model.ProductionRequestItem {
	items := make([]model.ProductionRequestItem, len(createItems))
	for i, createItem := range createItems {
		items[i] = model.ProductionRequestItem{
			ProductID: createItem.ProductID,
			Quantity:  createItem.Quantity,
		}
	}
	return items
}

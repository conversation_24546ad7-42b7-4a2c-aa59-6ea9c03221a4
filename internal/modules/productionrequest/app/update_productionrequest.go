package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
)

// Update implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) Update(ctx context.Context, productionRequestUpdate model.ProductionRequestUpdate) error {
	// Check if production request exists
	_, err := p.repo.GetByProp(ctx, "id", productionRequestUpdate.ID)
	if err != nil {
		return model.ProductionRequestNotFoundf("Production request not found", err, nil)
	}

	// Validate priority
	if !isValidPriority(productionRequestUpdate.Priority) {
		return model.ProductionRequestInvalidPriorityf("Invalid priority. Must be one of: low, medium, high, urgent", nil, nil)
	}

	// Validate state
	if !isValidState(productionRequestUpdate.State) {
		return model.ProductionRequestInvalidStatef("Invalid state. Must be one of: pending, approved, rejected, in_progress, completed", nil, nil)
	}

	// Validate expected date (should be in the future)
	if productionRequestUpdate.ExpectedDate != nil && productionRequestUpdate.ExpectedDate.Before(time.Now()) {
		return model.ProductionRequestInvalidDatef("Expected date must be in the future", nil, nil)
	}

	// Check if code already exists (excluding current production request)
	existingPR, err := p.repo.GetByProp(ctx, "code", productionRequestUpdate.Code)
	if err == nil && existingPR.ID != productionRequestUpdate.ID {
		return model.ProductionRequestConflictCodef("Production request code already exists", nil, nil)
	}

	// Validate client exists
	_, err = p.clientRepo.GetByProp(ctx, "id", productionRequestUpdate.ClientID)
	if err != nil {
		return model.ProductionRequestInvalidClientf("Client does not exist", err, nil)
	}

	// Validate products exist and quantities are positive
	for _, item := range productionRequestUpdate.Requests {
		if item.Quantity <= 0 {
			return model.ProductionRequestInvalidQuantityf("Product quantity must be greater than 0", nil, nil)
		}

		_, err := p.productRepo.GetByProp(ctx, "id", item.ProductID)
		if err != nil {
			return model.ProductionRequestInvalidProductf("Product does not exist", err, nil)
		}
	}

	productionRequest := model.ProductionRequest{
		ID:           productionRequestUpdate.ID,
		Code:         productionRequestUpdate.Code,
		ClientID:     productionRequestUpdate.ClientID,
		ExpectedDate: productionRequestUpdate.ExpectedDate,
		Priority:     productionRequestUpdate.Priority,
		State:        productionRequestUpdate.State,
		Requests:     convertItemsUpdateToItems(productionRequestUpdate.Requests),
	}

	return p.repo.Update(ctx, productionRequest)
}

func convertItemsUpdateToItems(updateItems []model.ProductionRequestItemUpdate) []model.ProductionRequestItem {
	items := make([]model.ProductionRequestItem, len(updateItems))
	for i, updateItem := range updateItems {
		items[i] = model.ProductionRequestItem{
			ID:        updateItem.ID,
			ProductID: updateItem.ProductID,
			Quantity:  updateItem.Quantity,
		}
	}
	return items
}

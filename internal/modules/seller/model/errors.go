package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	SellerConflictCode                     utils.ErrCode = utils.SellerCode + iota
	SellerConflictNameCode
	SellerConflictIdentityDocumentCode
	SellerNotFoundCode
)

func SellerConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(SellerConflictCode, message, err, details)
}

func SellerConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(SellerConflictNameCode, message, err, details)
}

func SellerConflictIdentityDocumentf(message string, err error, details any) utils.AppErr {
	return utils.NewError(SellerConflictIdentityDocumentCode, message, err, details)
}

func SellerNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(SellerNotFoundCode, message, err, details)
}

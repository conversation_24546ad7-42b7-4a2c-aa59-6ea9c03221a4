package model

import "context"

type SellerRepository interface {
	Create(ctx context.Context, seller Seller) error
	Update(ctx context.Context, seller Seller) error
	GetByProp(ctx context.Context, prop string, value string) (*Seller, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Seller, error)
	Delete(ctx context.Context, id string) error
}

package model

import "context"

type SellerUsecase interface {
	Create(ctx context.Context, seller SellerCreate) (string, error)
	Update(ctx context.Context, seller SellerUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Seller, error)
	GetAll(ctx context.Context) ([]Seller, error)
	Delete(ctx context.Context, id string) error
	ValidateIdentityDocument(ctx context.Context, identityDocument string) error
}

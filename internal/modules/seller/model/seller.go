package model

import "time"

type Seller struct {
	ID                     string
	Name                   string
	FatherName             string
	MotherName             string
	IdentityDocumentNumber string
	State                  string
	CreatedAt              *time.Time
	UpdatedAt              *time.Time
	DeletedAt              *time.Time
}

type SellerCreate struct {
	Name                   string
	FatherName             string
	MotherName             string
	IdentityDocumentNumber string
	State                  string
}

type SellerUpdate struct {
	ID                     string
	Name                   string
	FatherName             string
	MotherName             string
	IdentityDocumentNumber string
	State                  string
}

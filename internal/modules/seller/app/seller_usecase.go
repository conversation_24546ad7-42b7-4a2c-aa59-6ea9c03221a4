package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
)

type sellerUsecase struct {
	repo model.SellerRepository
}

func NewSellerUsecase(repo model.SellerRepository) model.SellerUsecase {
	return &sellerUsecase{
		repo: repo,
	}
}

// Delete implements model.SellerUsecase.
func (s *sellerUsecase) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// GetAll implements model.SellerUsecase.
func (s *sellerUsecase) GetAll(ctx context.Context) ([]model.Seller, error) {
	return s.repo.GetAll(ctx)
}

// GetByProp implements model.SellerUsecase.
func (s *sellerUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Seller, error) {
	return s.repo.GetByProp(ctx, prop, value)
}

// Update implements model.SellerUsecase.
func (s *sellerUsecase) Update(ctx context.Context, seller model.SellerUpdate) error {
	// Check if name already exists (excluding current seller)
	nameExists, err := s.repo.CountByProp(ctx, "name", seller.Name)
	if err != nil {
		return err
	}

	// Get current seller to check if name is being changed
	currentSeller, err := s.repo.GetByProp(ctx, "id", seller.ID)
	if err != nil {
		return model.SellerNotFoundf("Seller not found", err, nil)
	}

	// If name is being changed and new name already exists, return error
	if currentSeller.Name != seller.Name && nameExists > 0 {
		return model.SellerConflictNamef("Seller name already exists", nil, nil)
	}

	// Check if identity document already exists (excluding current seller)
	identityExists, err := s.repo.CountByProp(ctx, "identity_document_number", seller.IdentityDocumentNumber)
	if err != nil {
		return err
	}

	// If identity document is being changed and new identity document already exists, return error
	if currentSeller.IdentityDocumentNumber != seller.IdentityDocumentNumber && identityExists > 0 {
		return model.SellerConflictIdentityDocumentf("Identity document number already exists", nil, nil)
	}

	sellerEntity := model.Seller{
		ID:                     seller.ID,
		Name:                   seller.Name,
		FatherName:             seller.FatherName,
		MotherName:             seller.MotherName,
		IdentityDocumentNumber: seller.IdentityDocumentNumber,
		State:                  seller.State,
	}

	return s.repo.Update(ctx, sellerEntity)
}

// ValidateIdentityDocument implements model.SellerUsecase.
func (s *sellerUsecase) ValidateIdentityDocument(ctx context.Context, identityDocument string) error {
	count, err := s.repo.CountByProp(ctx, "identity_document_number", identityDocument)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.SellerConflictIdentityDocumentf("Identity document number already exists", nil, nil)
	}

	return nil
}

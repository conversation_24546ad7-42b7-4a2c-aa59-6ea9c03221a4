package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.SellerUsecase.
func (s *sellerUsecase) Create(ctx context.Context, seller model.SellerCreate) (string, error) {
	// Check if name already exists
	nameExists, err := s.repo.CountByProp(ctx, "name", seller.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if seller name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.SellerConflictNamef("Seller name already exists", nil, nil)
	}

	// Check if identity document already exists
	identityExists, err := s.repo.CountByProp(ctx, "identity_document_number", seller.IdentityDocumentNumber)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if identity document exists", err, nil)
	}

	if identityExists > 0 {
		return "", model.SellerConflictIdentityDocumentf("Identity document number already exists", nil, nil)
	}

	// Set default state if not provided
	state := seller.State
	if state == "" {
		state = "active"
	}

	newSeller := model.Seller{
		ID:                     utils.UniqueId(),
		Name:                   seller.Name,
		FatherName:             seller.FatherName,
		MotherName:             seller.MotherName,
		IdentityDocumentNumber: seller.IdentityDocumentNumber,
		State:                  state,
	}

	err = s.repo.Create(ctx, newSeller)
	if err != nil {
		return "", err
	}

	return newSeller.ID, nil
}

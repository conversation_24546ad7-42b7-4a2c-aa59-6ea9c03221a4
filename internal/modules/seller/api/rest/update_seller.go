package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type sellerUpdate struct {
	ID                     string `json:"id" validate:"required"`
	Name                   string `json:"name" validate:"required"`
	FatherName             string `json:"father_name" validate:"required"`
	MotherName             string `json:"mother_name" validate:"required"`
	IdentityDocumentNumber string `json:"identity_document_number" validate:"required"`
	State                  string `json:"state" validate:"required"`
}

func sellerUpdateToModel(dto sellerUpdate) model.SellerUpdate {
	return model.SellerUpdate{
		ID:                     dto.ID,
		Name:                   dto.Name,
		FatherName:             dto.FatherName,
		MotherName:             dto.MotherName,
		IdentityDocumentNumber: dto.IdentityDocumentNumber,
		State:                  dto.State,
	}
}

// Update implements SellerHandler.
func (s *sellerHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[sellerUpdate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	err = s.useCase.Update(ctx, sellerUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to update seller")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

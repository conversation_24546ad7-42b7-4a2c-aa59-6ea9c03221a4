package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type sellerCreate struct {
	Name                   string `json:"name" validate:"required"`
	FatherName             string `json:"father_name" validate:"required"`
	MotherName             string `json:"mother_name" validate:"required"`
	IdentityDocumentNumber string `json:"identity_document_number" validate:"required"`
	State                  string `json:"state"`
}

func sellerCreateToModel(dto sellerCreate) model.SellerCreate {
	state := dto.State
	if state == "" {
		state = "active" // Default value
	}
	return model.SellerCreate{
		Name:                   dto.Name,
		FatherName:             dto.FatherName,
		MotherName:             dto.MotherName,
		IdentityDocumentNumber: dto.IdentityDocumentNumber,
		State:                  state,
	}
}

// Create implements SellerHandler.
func (s *sellerHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[sellerCreate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	id, err := s.useCase.Create(ctx, sellerCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to create seller")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}

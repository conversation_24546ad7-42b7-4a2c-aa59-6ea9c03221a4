package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.SellerConflictCode:                 http.StatusConflict,
	model.SellerConflictNameCode:             http.StatusConflict,
	model.SellerConflictIdentityDocumentCode: http.StatusConflict,
	model.SellerNotFoundCode:                 http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}

package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type SellerHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateIdentityDocument(w http.ResponseWriter, r *http.Request)
}

type sellerHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.SellerUsecase
}

func NewSellerHandler(log *logrus.Logger, validator *validator.Validate, useCase model.SellerUsecase) SellerHandler {
	return &sellerHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}

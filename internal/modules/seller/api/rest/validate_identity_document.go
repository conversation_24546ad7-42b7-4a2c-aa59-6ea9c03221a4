package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateIdentityDocument implements SellerHandler.
func (s *sellerHandler) ValidateIdentityDocument(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	identityDocument := r.PathValue("identity_document")

	err := s.useCase.ValidateIdentityDocument(ctx, identityDocument)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Identity document validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type sellerResult struct {
	ID                     string     `json:"id"`
	Name                   string     `json:"name"`
	FatherName             string     `json:"father_name"`
	MotherName             string     `json:"mother_name"`
	IdentityDocumentNumber string     `json:"identity_document_number"`
	State                  string     `json:"state"`
	CreatedAt              *time.Time `json:"created_at"`
	UpdatedAt              *time.Time `json:"updated_at"`
	DeletedAt              *time.Time `json:"deleted_at"`
}

func sellerToResult(seller *model.Seller) sellerResult {
	return sellerResult{
		ID:                     seller.ID,
		Name:                   seller.Name,
		FatherName:             seller.FatherName,
		MotherName:             seller.MotherName,
		IdentityDocumentNumber: seller.IdentityDocumentNumber,
		State:                  seller.State,
		CreatedAt:              seller.CreatedAt,
		UpdatedAt:              seller.DeletedAt,
		DeletedAt:              seller.DeletedAt,
	}
}

// GetById implements SellerHandler.
func (s *sellerHandler) GetById(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")

	seller, err := s.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get seller")
		return
	}

	rest.SuccessDResponse(w, r, sellerToResult(seller), http.StatusOK)
}

// GetAll implements SellerHandler.
func (s *sellerHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	sellers, err := s.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get sellers")
		return
	}

	results := make([]sellerResult, len(sellers))
	for i, seller := range sellers {
		results[i] = sellerToResult(&seller)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

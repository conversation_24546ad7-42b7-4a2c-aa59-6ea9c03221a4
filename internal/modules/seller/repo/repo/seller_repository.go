package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/repo/pg"
)

type sellerRepository struct {
	pgRepo pg.SellerPostgreRepo
}

func NewSellerRepository(pgRepo pg.SellerPostgreRepo) model.SellerRepository {
	return &sellerRepository{
		pgRepo: pgRepo,
	}
}

// CountByProp implements model.SellerRepository.
func (s *sellerRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return s.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.SellerRepository.
func (s *sellerRepository) Create(ctx context.Context, seller model.Seller) error {
	return s.pgRepo.Create(ctx, seller)
}

// Delete implements model.SellerRepository.
func (s *sellerRepository) Delete(ctx context.Context, id string) error {
	return s.pgRepo.Delete(ctx, id)
}

// GetAll implements model.SellerRepository.
func (s *sellerRepository) GetAll(ctx context.Context) ([]model.Seller, error) {
	return s.pgRepo.GetAll(ctx)
}

// GetByProp implements model.SellerRepository.
func (s *sellerRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Seller, error) {
	return s.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.SellerRepository.
func (s *sellerRepository) Update(ctx context.Context, seller model.Seller) error {
	return s.pgRepo.Update(ctx, seller)
}

package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/seller/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

type SellerPostgreRepo interface {
	Create(ctx context.Context, seller model.Seller) error
	Update(ctx context.Context, seller model.Seller) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Seller, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Seller, error)
	Delete(ctx context.Context, id string) error
}

type sellerPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewSellerPostgreRepo(pool *pgxpool.Pool) SellerPostgreRepo {
	return &sellerPostgreRepo{
		pool: pool,
	}
}

// Create implements SellerPostgreRepo.
func (s *sellerPostgreRepo) Create(ctx context.Context, seller model.Seller) error {
	return pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO sellers (id, name, father_name, mother_name, identity_document_number, state, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		`
		_, err := conn.Exec(ctx, query, seller.ID, seller.Name, seller.FatherName, seller.MotherName, seller.IdentityDocumentNumber, seller.State)
		return err
	})
}

// Update implements SellerPostgreRepo.
func (s *sellerPostgreRepo) Update(ctx context.Context, seller model.Seller) error {
	return pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE sellers 
			SET name = $2, father_name = $3, mother_name = $4, identity_document_number = $5, state = $6, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`
		_, err := conn.Exec(ctx, query, seller.ID, seller.Name, seller.FatherName, seller.MotherName, seller.IdentityDocumentNumber, seller.State)
		return err
	})
}

// GetByProp implements SellerPostgreRepo.
func (s *sellerPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Seller, error) {
	var seller model.Seller
	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, father_name, mother_name, identity_document_number, state, created_at, updated_at, deleted_at
			FROM sellers 
			WHERE ` + prop + ` = $1 AND deleted_at IS NULL
		`
		row := conn.QueryRow(ctx, query, value)
		return row.Scan(&seller.ID, &seller.Name, &seller.FatherName, &seller.MotherName, &seller.IdentityDocumentNumber, &seller.State, &seller.CreatedAt, &seller.UpdatedAt, &seller.DeletedAt)
	})
	
	if err != nil {
		return nil, err
	}
	
	return &seller, nil
}

// CountByProp implements SellerPostgreRepo.
func (s *sellerPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `SELECT COUNT(*) FROM sellers WHERE ` + prop + ` = $1 AND deleted_at IS NULL`
		row := conn.QueryRow(ctx, query, value)
		return row.Scan(&count)
	})
	
	return count, err
}

// GetAll implements SellerPostgreRepo.
func (s *sellerPostgreRepo) GetAll(ctx context.Context) ([]model.Seller, error) {
	var sellers []model.Seller
	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, father_name, mother_name, identity_document_number, state, created_at, updated_at, deleted_at
			FROM sellers 
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`
		rows, err := conn.Query(ctx, query)
		if err != nil {
			return err
		}
		defer rows.Close()
		
		for rows.Next() {
			var seller model.Seller
			err := rows.Scan(&seller.ID, &seller.Name, &seller.FatherName, &seller.MotherName, &seller.IdentityDocumentNumber, &seller.State, &seller.CreatedAt, &seller.UpdatedAt, &seller.DeletedAt)
			if err != nil {
				return err
			}
			sellers = append(sellers, seller)
		}
		
		return rows.Err()
	})
	
	return sellers, err
}

// Delete implements SellerPostgreRepo.
func (s *sellerPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `UPDATE sellers SET deleted_at = CURRENT_TIMESTAMP WHERE id = $1 AND deleted_at IS NULL`
		_, err := conn.Exec(ctx, query, id)
		return err
	})
}

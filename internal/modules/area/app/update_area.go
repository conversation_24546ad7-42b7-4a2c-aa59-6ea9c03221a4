package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.AreaUsecase.
func (a *areaUsecase) Update(ctx context.Context, area model.AreaUpdate) error {
	// Get the current area to check if name has changed
	currentArea, err := a.repo.GetByProp(ctx, "id", area.ID)
	if err != nil {
		return err
	}

	// Check if name has changed and if it's already in use
	if currentArea.Name != area.Name {
		nameExists, err := a.repo.CountByProp(ctx, "name", area.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if area name exists", err, nil)
		}

		if nameExists > 0 {
			return model.AreaConflictNamef("Area name already exists", nil, nil)
		}
	}

	updatedArea := model.Area{
		ID:           area.ID,
		Name:         area.Name,
		DeliveryDays: area.DeliveryDays,
		State:        area.State,
	}

	err = a.repo.Update(ctx, updatedArea)
	if err != nil {
		return err
	}

	return nil
}

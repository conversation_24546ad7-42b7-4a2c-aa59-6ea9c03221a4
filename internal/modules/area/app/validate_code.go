package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
)

// ValidateCode implements model.AreaUsecase.
func (a *areaUsecase) ValidateCode(ctx context.Context, code string) error {
	// Check if name exists (since area doesn't have a code field, we validate name)
	count, err := a.repo.CountByProp(ctx, "name", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.AreaConflictNamef("Area name already exists", nil, nil)
	}

	return nil
}

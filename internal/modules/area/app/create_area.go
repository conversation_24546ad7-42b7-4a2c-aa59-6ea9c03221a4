package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.AreaUsecase.
func (a *areaUsecase) Create(ctx context.Context, area model.AreaCreate) (string, error) {
	// Check if name already exists
	nameExists, err := a.repo.CountByProp(ctx, "name", area.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if area name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.AreaConflictNamef("Area name already exists", nil, nil)
	}

	// Set default state if not provided
	state := area.State
	if state == "" {
		state = "active"
	}

	newArea := model.Area{
		ID:           utils.UniqueId(),
		Name:         area.Name,
		DeliveryDays: area.DeliveryDays,
		State:        state,
	}

	err = a.repo.Create(ctx, newArea)
	if err != nil {
		return "", err
	}

	return newArea.ID, nil
}

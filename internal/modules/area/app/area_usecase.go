package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
)

type areaUsecase struct {
	repo model.AreaRepository
}

// Delete implements model.AreaUsecase.
func (a *areaUsecase) Delete(ctx context.Context, id string) error {
	return a.repo.Delete(ctx, id)
}

// GetAll implements model.AreaUsecase.
func (a *areaUsecase) GetAll(ctx context.Context) ([]model.Area, error) {
	return a.repo.GetAll(ctx)
}

// GetByProp implements model.AreaUsecase.
func (a *areaUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Area, error) {
	return a.repo.GetByProp(ctx, prop, value)
}

func NewAreaUsecase(repo model.AreaRepository) model.AreaUsecase {
	return &areaUsecase{
		repo: repo,
	}
}

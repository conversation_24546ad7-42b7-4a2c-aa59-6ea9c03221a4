package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type areaUpdate struct {
	ID           string `json:"id" validate:"required"`
	Name         string `json:"name" validate:"required,min=1,max=255"`
	DeliveryDays []int  `json:"delivery_days" validate:"required,dive,min=0,max=6"`
	State        string `json:"state" validate:"required,oneof=active inactive"`
}

func areaUpdateToModel(req areaUpdate) model.AreaUpdate {
	return model.AreaUpdate{
		ID:           req.ID,
		Name:         req.Name,
		DeliveryDays: req.DeliveryDays,
		State:        req.State,
	}
}

// Update implements AreaHandler.
func (a *areaHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[areaUpdate](w, r, a.validator)
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		return
	}

	err = a.useCase.Update(ctx, areaUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		respErrHandler(w, r, err, "Failed to update area")
		return
	}

	rest.SuccessDResponse(w, r, "Area updated successfully", http.StatusOK)
}

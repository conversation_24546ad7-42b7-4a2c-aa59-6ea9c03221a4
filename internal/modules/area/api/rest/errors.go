package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var areaErrHandlers = rest.RespErrHandlers{
	model.AreaConflictCode:     http.StatusConflict,
	model.AreaConflictNameCode: http.StatusConflict,
	model.AreaNotFoundCode:     http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, areaErrHandlers)
}

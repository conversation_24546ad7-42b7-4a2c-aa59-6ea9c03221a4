package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements AreaHandler.
func (a *areaHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	err := a.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		respErrHandler(w, r, err, "Failed to delete area")
		return
	}

	rest.SuccessDResponse(w, r, "Area deleted successfully", http.StatusOK)
}

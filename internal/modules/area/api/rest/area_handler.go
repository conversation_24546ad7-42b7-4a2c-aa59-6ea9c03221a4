package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type AreaHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
}

type areaHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.AreaUsecase
}

func NewAreaHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.AreaUsecase,
) AreaHandler {
	return &areaHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}

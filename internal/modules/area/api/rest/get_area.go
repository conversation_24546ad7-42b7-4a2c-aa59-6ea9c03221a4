package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type areaResult struct {
	ID           string     `json:"id"`
	Name         string     `json:"name"`
	DeliveryDays []int      `json:"delivery_days"`
	State        string     `json:"state"`
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
	DeletedAt    *time.Time `json:"deleted_at"`
}

func areaToResult(area *model.Area) areaResult {
	return areaResult{
		ID:           area.ID,
		Name:         area.Name,
		DeliveryDays: area.DeliveryDays,
		State:        area.State,
		CreatedAt:    area.CreatedAt,
		UpdatedAt:    area.UpdatedAt,
		DeletedAt:    area.DeletedAt,
	}
}

// GetById implements AreaHandler.
func (a *areaHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	area, err := a.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		respErrHandler(w, r, err, "Failed to get area by id")
		return
	}

	rest.SuccessDResponse(w, r, areaToResult(area), http.StatusOK)
}

// GetAll implements AreaHandler.
func (a *areaHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	areas, err := a.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		respErrHandler(w, r, err, "Failed to get areas")
		return
	}

	results := make([]areaResult, len(areas))
	for i, area := range areas {
		results[i] = areaToResult(&area)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateCode implements AreaHandler.
func (a *areaHandler) ValidateCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	err := a.useCase.ValidateCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		respErrHandler(w, r, err, "Failed to validate area code")
		return
	}

	rest.SuccessDResponse(w, r, "Area code is valid", http.StatusOK)
}

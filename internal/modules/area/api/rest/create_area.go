package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type areaCreate struct {
	Name         string `json:"name" validate:"required,min=1,max=255"`
	DeliveryDays []int  `json:"delivery_days" validate:"required,dive,min=0,max=6"`
	State        string `json:"state" validate:"omitempty,oneof=active inactive"`
}

func areaCreateToModel(req areaCreate) model.AreaCreate {
	return model.AreaCreate{
		Name:         req.Name,
		DeliveryDays: req.DeliveryDays,
		State:        req.State,
	}
}

// Create implements AreaHandler.
func (a *areaHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[areaCreate](w, r, a.validator)
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		return
	}

	id, err := a.useCase.Create(ctx, areaCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		respErrHandler(w, r, err, "Failed to create area")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}

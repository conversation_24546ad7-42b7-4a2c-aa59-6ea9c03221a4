package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	pg "github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (a *areaPostgreRepo) Update(ctx context.Context, area model.Area) error {
	return pg.ExecuteInSchema(ctx, a.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE areas
			SET name = $2, delivery_days = $3, state = $4, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			area.ID,
			area.Name,
			area.DeliveryDays,
			area.State,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update area", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.AreaNotFoundf("Area not found", nil, nil)
		}

		return nil
	})
}

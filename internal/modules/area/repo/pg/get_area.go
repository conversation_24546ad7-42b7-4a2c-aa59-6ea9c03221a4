package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	pg "github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (a *areaPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Area, error) {
	var area model.Area
	err := pg.ExecuteInSchema(ctx, a.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT id, name, delivery_days, state, created_at, updated_at, deleted_at
			FROM areas
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&area.ID,
			&area.Name,
			&area.DeliveryDays,
			&area.State,
			&area.CreatedAt,
			&area.UpdatedAt,
			&area.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.AreaNotFoundf("Area not found", err, nil)
			}
			return utils.InternalErrorf("failed to get area", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &area, nil
}

func (a *areaPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, a.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM areas
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)

		if err != nil {
			return utils.InternalErrorf("failed to count areas", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (a *areaPostgreRepo) GetAll(ctx context.Context) ([]model.Area, error) {
	var areas []model.Area
	err := pg.ExecuteInSchema(ctx, a.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, delivery_days, state, created_at, updated_at, deleted_at
			FROM areas
			WHERE deleted_at IS NULL
			ORDER BY name
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get areas", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var area model.Area
			err := rows.Scan(
				&area.ID,
				&area.Name,
				&area.DeliveryDays,
				&area.State,
				&area.CreatedAt,
				&area.UpdatedAt,
				&area.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan area", err, nil)
			}
			areas = append(areas, area)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return areas, nil
}

package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type AreaPostgreRepo interface {
	Create(ctx context.Context, area model.Area) error
	Update(ctx context.Context, area model.Area) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Area, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Area, error)
	Delete(ctx context.Context, id string) error
}

type areaPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewAreaPostgreRepo(pool *pgxpool.Pool) AreaPostgreRepo {
	return &areaPostgreRepo{
		pool: pool,
	}
}

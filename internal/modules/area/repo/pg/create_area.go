package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	pg "github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (a *areaPostgreRepo) Create(ctx context.Context, area model.Area) error {
	return pg.ExecuteInSchema(ctx, a.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO areas (id, name, delivery_days, state)
			VALUES ($1, $2, $3, $4)
		`

		_, err := conn.Exec(ctx, query,
			area.ID,
			area.Name,
			area.DeliveryDays,
			area.State,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create area", err, nil)
		}

		return nil
	})
}

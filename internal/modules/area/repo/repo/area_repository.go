package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/area/repo/pg"
)

type areaRepository struct {
	pgRepo pg.AreaPostgreRepo
}

// CountByProp implements model.AreaRepository.
func (a *areaRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return a.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.AreaRepository.
func (a *areaRepository) Create(ctx context.Context, area model.Area) error {
	return a.pgRepo.Create(ctx, area)
}

// Delete implements model.AreaRepository.
func (a *areaRepository) Delete(ctx context.Context, id string) error {
	return a.pgRepo.Delete(ctx, id)
}

// GetAll implements model.AreaRepository.
func (a *areaRepository) GetAll(ctx context.Context) ([]model.Area, error) {
	return a.pgRepo.GetAll(ctx)
}

// GetByProp implements model.AreaRepository.
func (a *areaRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Area, error) {
	return a.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.AreaRepository.
func (a *areaRepository) Update(ctx context.Context, area model.Area) error {
	return a.pgRepo.Update(ctx, area)
}

func NewAreaRepository(pgRepo pg.AreaPostgreRepo) model.AreaRepository {
	return &areaRepository{
		pgRepo: pgRepo,
	}
}

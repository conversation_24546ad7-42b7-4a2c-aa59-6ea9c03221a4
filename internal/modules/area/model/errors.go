package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	AreaConflictCode     utils.ErrCode = utils.AreaCode + iota
	AreaConflictNameCode
	AreaNotFoundCode
)

func AreaConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(AreaConflictCode, message, err, details)
}

func AreaConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(AreaConflictNameCode, message, err, details)
}

func AreaNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(AreaNotFoundCode, message, err, details)
}

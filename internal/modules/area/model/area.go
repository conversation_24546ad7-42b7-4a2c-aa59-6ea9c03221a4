package model

import "time"

type Area struct {
	ID           string
	Name         string
	DeliveryDays []int // Array of numbers where Sunday=0, Monday=1, etc.
	State        string
	CreatedAt    *time.Time
	UpdatedAt    *time.Time
	DeletedAt    *time.Time
}

type AreaCreate struct {
	Name         string
	DeliveryDays []int
	State        string
}

type AreaUpdate struct {
	ID           string
	Name         string
	DeliveryDays []int
	State        string
}

-- Migration: Add seller_id to clients table
-- Description: Add seller_id column to clients table with foreign key reference to sellers table
-- Date: 2025-08-07

-- Add seller_id column to clients table
ALTER TABLE dev.clients 
ADD COLUMN seller_id VARCHAR(255);

-- Add foreign key constraint to reference sellers table
ALTER TABLE dev.clients 
ADD CONSTRAINT fk_clients_seller_id 
FOREIGN KEY (seller_id) REFERENCES dev.sellers(id);

-- Add index for better query performance
CREATE INDEX idx_clients_seller_id ON dev.clients(seller_id);

-- Optional: Add comment to document the relationship
COMMENT ON COLUMN dev.clients.seller_id IS 'Foreign key reference to sellers table - identifies the seller associated with this client';
